# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

apiVersion: gateway.networking.k8s.io/v1
kind: GatewayClass
metadata:
  name: envoy-ai-gateway-basic
spec:
  controllerName: gateway.envoyproxy.io/gatewayclass-controller
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: envoy-ai-gateway-basic
  namespace: default
spec:
  gatewayClassName: envoy-ai-gateway-basic
  listeners:
    - name: http
      protocol: HTTP
      port: 80
---
apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: AIGatewayRoute
metadata:
  name: envoy-ai-gateway-basic
  namespace: default
spec:
  schema:
    name: OpenAI
  targetRefs:
    - name: envoy-ai-gateway-basic
      kind: Gateway
      group: gateway.networking.k8s.io
  rules:
    - matches:
        - headers:
            - type: Exact
              name: x-ai-eg-model
              value: deepseek-r1
      backendRefs:
        - name: envoy-ai-gateway-basic-deepseek
    - matches:
        - headers:
            - type: Exact
              name: x-ai-eg-model
              value: hunyuan-turbos-latest
      backendRefs:
        - name: envoy-ai-gateway-basic-hunyuan
    - matches:
        - headers:
            - type: Exact
              name: x-ai-eg-model
              value: self-hosted-model
      backendRefs:
        - name: envoy-ai-gateway-basic-testupstream
---
apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: AIServiceBackend
metadata:
  name: envoy-ai-gateway-basic-deepseek
  namespace: default
spec:
  schema:
    name: OpenAI
  backendRef:
    name: envoy-ai-gateway-basic-deepseek
    kind: Backend
    group: gateway.envoyproxy.io
  backendSecurityPolicyRef:
    name: envoy-ai-gateway-basic-deepseek-apikey
    kind: BackendSecurityPolicy
    group: aigateway.envoyproxy.io
---
apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: AIServiceBackend
metadata:
  name: envoy-ai-gateway-basic-hunyuan
  namespace: default
spec:
  schema:
    name: OpenAI
  backendRef:
    name: envoy-ai-gateway-basic-hunyuan
    kind: Backend
    group: gateway.envoyproxy.io
  backendSecurityPolicyRef:
    name: envoy-ai-gateway-basic-hunyuan-apikey
    kind: BackendSecurityPolicy
    group: aigateway.envoyproxy.io
---
apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: BackendSecurityPolicy
metadata:
  name: envoy-ai-gateway-basic-deepseek-apikey
  namespace: default
spec:
  type: APIKey
  apiKey:
    secretRef:
      name: envoy-ai-gateway-basic-deepseek-apikey
      namespace: default
---
apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: BackendSecurityPolicy
metadata:
  name: envoy-ai-gateway-basic-hunyuan-apikey
  namespace: default
spec:
  type: APIKey
  apiKey:
    secretRef:
      name: envoy-ai-gateway-basic-hunyuan-apikey
      namespace: default
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: Backend
metadata:
  name: envoy-ai-gateway-basic-deepseek
  namespace: default
spec:
  endpoints:
    - fqdn:
        hostname: api.lkeap.cloud.tencent.com
        port: 443
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: Backend
metadata:
  name: envoy-ai-gateway-basic-hunyuan
  namespace: default
spec:
  endpoints:
    - fqdn:
        hostname: api.hunyuan.cloud.tencent.com
        port: 443
---
apiVersion: gateway.networking.k8s.io/v1alpha3
kind: BackendTLSPolicy
metadata:
  name: envoy-ai-gateway-basic-deepseek-tls
  namespace: default
spec:
  targetRefs:
    - group: 'gateway.envoyproxy.io'
      kind: Backend
      name: envoy-ai-gateway-basic-deepseek
  validation:
    wellKnownCACertificates: "System"
    hostname: api.lkeap.cloud.tencent.com
---
apiVersion: gateway.networking.k8s.io/v1alpha3
kind: BackendTLSPolicy
metadata:
  name: envoy-ai-gateway-basic-hunyuan-tls
  namespace: default
spec:
  targetRefs:
    - group: 'gateway.envoyproxy.io'
      kind: Backend
      name: envoy-ai-gateway-basic-hunyuan
  validation:
    wellKnownCACertificates: "System"
    hostname: api.hunyuan.cloud.tencent.com
---
apiVersion: v1
kind: Secret
metadata:
  name: envoy-ai-gateway-basic-deepseek-apikey
  namespace: default
type: Opaque
stringData:
  apiKey: sk-HVKZjOoUBiv9r4slY581Hafpy3Fk7A3hV8pN1CAAmjKuC2QG  # Replace with your OpenAI API key.
---
apiVersion: v1
kind: Secret
metadata:
  name: envoy-ai-gateway-basic-hunyuan-apikey
  namespace: default
type: Opaque
stringData:
  apiKey: sk-1mTt4x8ktHOLgKNLS9c7z6998DZYDlKFAaKJlQ5j1Iikq47h  # Replace with your OpenAI API key.
---
apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: AIServiceBackend
metadata:
  name: envoy-ai-gateway-basic-testupstream
  namespace: default
spec:
  schema:
    name: OpenAI
  backendRef:
    name: envoy-ai-gateway-basic-testupstream
    kind: Backend
    group: gateway.envoyproxy.io
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: Backend
metadata:
  name: envoy-ai-gateway-basic-testupstream
  namespace: default
spec:
  endpoints:
    - fqdn:
        hostname: envoy-ai-gateway-basic-testupstream.default.svc.cluster.local
        port: 80
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: envoy-ai-gateway-basic-testupstream
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: envoy-ai-gateway-basic-testupstream
  template:
    metadata:
      labels:
        app: envoy-ai-gateway-basic-testupstream
    spec:
      containers:
        - name: testupstream
          image: docker.io/envoyproxy/ai-gateway-testupstream:latest
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 8080
          env:
            - name: TESTUPSTREAM_ID
              value: test
          readinessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 1
            periodSeconds: 1
---
apiVersion: v1
kind: Service
metadata:
  name: envoy-ai-gateway-basic-testupstream
  namespace: default
spec:
  selector:
    app: envoy-ai-gateway-basic-testupstream
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8080
  type: ClusterIP
